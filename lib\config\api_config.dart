import 'package:flutter/foundation.dart';

/// Environment types for the application
enum Environment { development, staging, production }

/// Centralized API configuration class
/// Manages all API endpoints and environment-specific configurations
class ApiConfig {
  // Private constructor to prevent instantiation
  ApiConfig._();

  /// Current environment - can be changed for testing or different builds
  static Environment _currentEnvironment = Environment.development;

  /// Get current environment
  static Environment get currentEnvironment => _currentEnvironment;

  /// Set current environment
  static void setEnvironment(Environment environment) {
    _currentEnvironment = environment;
    if (kDebugMode) {
      print('API Environment switched to: ${environment.name}');
      print('Base URL: $baseUrl');
    }
  }

  /// Environment-specific base URLs
  static const Map<Environment, String> _baseUrls = {
    Environment.development: 'https://dapi-test.adscloud.org:8443',
    // Environment.development: 'https://dapi.adscloud.org',
    Environment.staging: 'https://dapi.adscloud.org', // Update when available
    Environment.production:
        'https://dapi.adscloud.org', // Update when available
  };

  /// Environment-specific WebSocket URLs
  static const Map<Environment, String> _webSocketUrls = {
    Environment.development: 'wss://dapi-test.adscloud.org:8443',
    // Environment.development: 'wss://dapi.adscloud.org',
    Environment.staging: 'wss://dapi.adscloud.org', // Update when available
    Environment.production: 'wss://dapi.adscloud.org', // Update when available
    // Environment.staging:
    //     'wss://dapi-staging.adscloud.org:8443', // Update when available
    // Environment.production: 'wss://api.dalti.com', // Update when available
  };

  /// Get current base URL based on environment
  static String get baseUrl => _baseUrls[_currentEnvironment]!;

  /// Get current WebSocket URL based on environment
  static String get webSocketBaseUrl => _webSocketUrls[_currentEnvironment]!;

  // ============================================================================
  // API ENDPOINT PATHS
  // ============================================================================

  /// Authentication endpoints
  static const String loginPath = '/api/auth/login';
  static const String userProfilePath = '/auth/me';
  static const String userExistsPath = '/api/auth/user-exists';
  static const String requestPasswordResetOtpPath =
      '/api/auth/request-password-reset-otp';
  static const String verifyPasswordResetOtpPath =
      '/api/auth/verify-password-reset-otp';
  static const String resetPasswordPath = '/api/auth/reset-password';

  /// Customer endpoints
  static const String customerAddressesPath = '/api/auth/customer/addresses';

  /// User endpoints
  static const String updatePreferredLanguagePath =
      '/api/auth/user/update-prefered-language';

  /// Provider endpoints
  static const String searchProvidersPath = '/api/search/providers';
  static const String providerProfilePath =
      '/api/public/provider'; // Add /{providerId}
  static const String providerAvailabilityPath = '/api/provider-availability';

  /// Booking endpoints
  static const String customerBookingPath =
      '/api/auth/appointments/customer-booking';
  static const String customerAppointmentsPath =
      '/api/auth/customer/appointments';

  /// Notification endpoints
  static const String notificationsListPath =
      '/api/auth/notifications/mobile/list';
  static const String markNotificationReadPath =
      '/api/auth/notifications/mobile/mark-as-read';
  static const String markAllNotificationsReadPath =
      '/api/auth/notifications/mobile/mark-all-as-read';

  /// Advertisement endpoints
  static const String advertisementsPath = '/api/auth/public/advertisements';

  /// FCM endpoints
  static const String fcmTokenPath = '/api/auth/fcm-token';
  static const String saveFcmTokenPath =
      '/api/auth/notifications/mobile/save-fcm-token';

  /// Queue endpoints
  static const String queuePath = '/api/auth/queue';
  static const String queueRequestSwapPath = '/api/auth/queue/request-swap';
  static const String queueRespondSwapPath = '/api/auth/queue/respond-swap';

  /// Review endpoints
  static const String reviewsPath = '/api/auth/reviews';
  static const String publicProviderReviewsPath =
      '/api/public/providers'; // Add /{providerId}/reviews

  /// Mobile messaging endpoints
  static const String mobileConversationsPath =
      '/api/auth/mobile/conversations';
  static const String mobileMessagesPath = '/api/auth/mobile/messages';
  static const String mobileMarkMessageReadPath =
      '/api/auth/mobile/messages/read';

  /// WebSocket endpoints
  static const String socketIoPath = '/socket.io/?EIO=4&transport=websocket';

  // ============================================================================
  // FULL URL BUILDERS
  // ============================================================================

  /// Build full API URL
  static String buildApiUrl(String path) {
    return '$baseUrl$path';
  }

  /// Build WebSocket URL
  static String buildWebSocketUrl({String? sessionId}) {
    final baseWsUrl = '$webSocketBaseUrl$socketIoPath';
    if (sessionId != null && sessionId.isNotEmpty) {
      return '$baseWsUrl&sessionId=$sessionId';
    }
    return baseWsUrl;
  }

  /// Build provider profile URL
  static String buildProviderProfileUrl(String providerId) {
    return buildApiUrl('$providerProfilePath/$providerId');
  }

  /// Build customer address URL with ID
  static String buildCustomerAddressUrl(String addressId) {
    return buildApiUrl('$customerAddressesPath/$addressId');
  }

  /// Build public provider reviews URL
  static String buildPublicProviderReviewsUrl(int providerId) {
    return buildApiUrl('$publicProviderReviewsPath/$providerId/reviews');
  }

  // ============================================================================
  // COMMONLY USED FULL URLS
  // ============================================================================

  /// Authentication URLs
  static String get loginUrl => buildApiUrl(loginPath);
  static String get userProfileUrl => buildApiUrl(userProfilePath);
  static String get userExistsUrl => buildApiUrl(userExistsPath);
  static String get requestPasswordResetOtpUrl =>
      buildApiUrl(requestPasswordResetOtpPath);
  static String get verifyPasswordResetOtpUrl =>
      buildApiUrl(verifyPasswordResetOtpPath);
  static String get resetPasswordUrl => buildApiUrl(resetPasswordPath);

  /// Customer URLs
  static String get customerAddressesUrl => buildApiUrl(customerAddressesPath);

  /// User URLs
  static String get updatePreferredLanguageUrl =>
      buildApiUrl(updatePreferredLanguagePath);

  /// Provider URLs
  static String get searchProvidersUrl => buildApiUrl(searchProvidersPath);
  static String get providerAvailabilityUrl =>
      buildApiUrl(providerAvailabilityPath);

  /// Booking URLs
  static String get customerBookingUrl => buildApiUrl(customerBookingPath);
  static String get customerAppointmentsUrl =>
      buildApiUrl(customerAppointmentsPath);

  /// Notification URLs
  static String get notificationsListUrl => buildApiUrl(notificationsListPath);
  static String get markNotificationReadUrl =>
      buildApiUrl(markNotificationReadPath);
  static String get markAllNotificationsReadUrl =>
      buildApiUrl(markAllNotificationsReadPath);

  /// Advertisement URLs
  static String get advertisementsUrl => buildApiUrl(advertisementsPath);

  /// FCM URLs
  static String get fcmTokenUrl => buildApiUrl(fcmTokenPath);
  static String get saveFcmTokenUrl => buildApiUrl(saveFcmTokenPath);

  /// Queue URLs
  static String get queueUrl => buildApiUrl(queuePath);
  static String get queueRequestSwapUrl => buildApiUrl(queueRequestSwapPath);
  static String get queueRespondSwapUrl => buildApiUrl(queueRespondSwapPath);

  /// Review URLs
  static String get reviewsUrl => buildApiUrl(reviewsPath);

  /// Mobile messaging URLs
  static String get mobileConversationsUrl =>
      buildApiUrl(mobileConversationsPath);
  static String get mobileMessagesUrl => buildApiUrl(mobileMessagesPath);
  static String get mobileMarkMessageReadUrl =>
      buildApiUrl(mobileMarkMessageReadPath);

  /// Build mobile messages URL for specific conversation
  static String buildMobileMessagesUrl(int conversationId) {
    return buildApiUrl('$mobileMessagesPath/$conversationId');
  }

  // ============================================================================
  // UTILITY METHODS
  // ============================================================================

  /// Check if current environment is development
  static bool get isDevelopment =>
      _currentEnvironment == Environment.development;

  /// Check if current environment is staging
  static bool get isStaging => _currentEnvironment == Environment.staging;

  /// Check if current environment is production
  static bool get isProduction => _currentEnvironment == Environment.production;

  /// Get environment name as string
  static String get environmentName => _currentEnvironment.name;

  /// Print current configuration (for debugging)
  static void printCurrentConfig() {
    if (kDebugMode) {
      print('=== API Configuration ===');
      print('Environment: $environmentName');
      print('Base URL: $baseUrl');
      print('WebSocket URL: $webSocketBaseUrl');
      print('========================');
    }
  }
}
