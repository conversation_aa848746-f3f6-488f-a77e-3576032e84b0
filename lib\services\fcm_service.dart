import 'dart:convert'; // Added for jsonEncode
import 'dart:io'; // Added for Platform detection

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart'; // We'll add this for foreground notifications
import 'package:http/http.dart' as http; // Added for HTTP requests
import 'package:shared_preferences/shared_preferences.dart'; // Added for SharedPreferences
import 'package:dalti/base/constant.dart'; // For prefsKeySessionId
import 'package:dalti/config/api_config.dart';
import 'package:dalti/config/network_config.dart';

// TODO: Handle notification tapped when app is terminated

const String prefsKeyFcmToken =
    'fcm_token'; // Key for storing FCM token locally

class FcmService {
  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;

  // For displaying foreground notifications on Android/iOS
  // iOS foreground notifications are handled differently if you want them to appear as system notifications
  // while the app is open. For Android, flutter_local_notifications is needed.
  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  Future<void> init() async {
    try {
      if (kDebugMode) {
        print('Initializing FCM Service...');
      }

      await _requestPermissions();
      await _getAndRegisterFcmToken(); // Renamed for clarity
      _initForegroundNotifications();
      _setupMessageHandlers();
      _createAndroidNotificationChannel(); // Create channel for Android

      if (kDebugMode) {
        print('FCM Service initialized successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error initializing FCM Service: $e');
      }
      // Don't throw the error, just log it so the app can continue
    }
  }

  Future<void> _requestPermissions() async {
    NotificationSettings settings = await _firebaseMessaging.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );

    if (kDebugMode) {
      print('User granted permission: ${settings.authorizationStatus}');
    }
  }

  Future<void> _getAndRegisterFcmToken() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      String? storedToken = prefs.getString(prefsKeyFcmToken);

      if (storedToken != null && storedToken.isNotEmpty) {
        if (kDebugMode) {
          print('Using stored FCM Token: $storedToken');
        }
        // Optionally, re-send stored token to server to ensure it's up-to-date there
        // This can be useful if the app was reinstalled or if a previous send failed silently.
        await _sendTokenToServer(storedToken);
      } else {
        if (kDebugMode) {
          print('No stored FCM token found, fetching new token.');
        }

        try {
          String? newToken = await _firebaseMessaging.getToken();
          if (kDebugMode) {
            print('New FCM Token: $newToken');
          }

          if (newToken != null) {
            await prefs.setString(prefsKeyFcmToken, newToken);
            await _sendTokenToServer(newToken);
          } else {
            if (kDebugMode) {
              print('Failed to get new FCM token from Firebase.');
            }
          }
        } catch (tokenError) {
          if (kDebugMode) {
            print('Error getting FCM token: $tokenError');
          }
          // Don't throw the error, just log it and continue
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error in _getAndRegisterFcmToken: $e');
      }
      // Don't throw the error, just log it and continue
    }
  }

  Future<void> _sendTokenToServer(String token) async {
    final prefs = await SharedPreferences.getInstance();
    final sessionId = prefs.getString(
      prefsKeySessionId,
    ); // Using the key from auth_utils.dart

    if (sessionId == null || sessionId.isEmpty) {
      if (kDebugMode) {
        print('Session ID not found. Cannot send FCM token to server.');
      }
      return;
    }

    String deviceType;

    if (kIsWeb) {
      deviceType = 'web';
      // return; // FCM token saving to custom server is typically not done for web in this manner
    } else if (Platform.isAndroid) {
      deviceType = 'android';
    } else if (Platform.isIOS) {
      deviceType = 'ios';
    } else {
      if (kDebugMode) {
        print('Unsupported platform for FCM token saving.');
      }
      return;
    }

    final String apiUrl = ApiConfig.saveFcmTokenUrl;
    final Map<String, String> headers = {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $sessionId',
    };
    final Map<String, String> body = {'token': token, 'deviceType': deviceType};

    try {
      final response = await http.post(
        Uri.parse(apiUrl),
        headers: headers,
        body: jsonEncode(body),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (kDebugMode) {
          print('FCM token $token saved to server successfully.');
        }
      } else {
        if (kDebugMode) {
          print(
            'Failed to save FCM token $token. Status: ${response.statusCode} Body: ${response.body}',
          );
        }
        // Optionally, implement retry logic or inform the user
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error sending FCM token $token to server: $e');
      }
      // Optionally, implement retry logic or inform the user
    }
  }

  void _initForegroundNotifications() async {
    // Initialization settings for Android
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings(
          '@mipmap/ic_launcher',
        ); // Use the same icon as in AndroidManifest

    // Initialization settings for iOS.
    // Note: For iOS foreground notifications to appear, you might need further handling
    // or to decide if you want them to appear as heads-up notifications while the app is open.
    // Firebase handles them by default if the app is in the background or terminated.
    const DarwinInitializationSettings
    initializationSettingsIOS = DarwinInitializationSettings(
      requestAlertPermission:
          false, // Permissions are requested via FirebaseMessaging.instance.requestPermission()
      requestBadgePermission: false,
      requestSoundPermission: false,
      // onDidReceiveLocalNotification: onDidReceiveLocalNotification, // Optional: for older iOS versions
    );

    const InitializationSettings initializationSettings =
        InitializationSettings(
          android: initializationSettingsAndroid,
          iOS: initializationSettingsIOS,
        );

    await _flutterLocalNotificationsPlugin.initialize(
      initializationSettings,
      // onDidReceiveNotificationResponse: onNotificationTapped, // TODO: Handle notification tap
    );
  }

  Future<void> _createAndroidNotificationChannel() async {
    // Create the channel we defined in AndroidManifest.xml
    // This is only necessary for Android 8.0 (API level 26) and higher.
    const AndroidNotificationChannel channel = AndroidNotificationChannel(
      'default_channel_id', // Same ID as in AndroidManifest.xml
      'Default Channel', // Title for user settings
      description:
          'This channel is used for important notifications.', // Description for user settings
      importance: Importance.high,
      // playSound: true, // You can add sound here or per notification
      // sound: RawResourceAndroidNotificationSound('notification_sound'), // Example custom sound
    );

    await _flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
          AndroidFlutterLocalNotificationsPlugin
        >()
        ?.createNotificationChannel(channel);
    if (kDebugMode) {
      print('Default notification channel created.');
    }
  }

  void _showForegroundNotification(RemoteMessage message) {
    RemoteNotification? notification = message.notification;
    AndroidNotification? android = message.notification?.android;

    if (notification != null && android != null && !kIsWeb) {
      // kIsWeb check if you plan for web
      _flutterLocalNotificationsPlugin.show(
        notification.hashCode,
        notification.title,
        notification.body,
        NotificationDetails(
          android: AndroidNotificationDetails(
            'default_channel_id', // Same channel ID
            'Default Channel',
            channelDescription:
                'This channel is used for important notifications.',
            icon:
                android.smallIcon ??
                '@mipmap/ic_launcher', // Or your custom icon
            // other properties...
          ),
        ),
        // payload: message.data['route'], // Example: if you send 'route' in data payload for navigation
      );
    }
  }

  void _setupMessageHandlers() {
    // 1. Handle messages when app is in foreground
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      if (kDebugMode) {
        print('Got a message whilst in the foreground!');
        print('Message data: ${message.data}');
      }

      if (message.notification != null) {
        if (kDebugMode) {
          print(
            'Message also contained a notification: ${message.notification}',
          );
        }
        // Display the notification using flutter_local_notifications
        // because foreground FCM notifications are not shown by default on Android.
        _showForegroundNotification(message);
      }
    });

    // 2. Handle messages when app is in background (but not terminated)
    // When a background message is received, and it contains a notification payload,
    // Firebase automatically displays a system notification.
    // If you tap that notification, onMessageOpenedApp will be triggered.
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      if (kDebugMode) {
        print('A new onMessageOpenedApp event was published!');
        print('Message data: ${message.data}');
      }
      // TODO: Navigate to a specific screen based on message.data or handle the tap
      // Example: if (message.data['route'] != null) { Get.toNamed(message.data['route']); }
    });

    // 3. Handle messages when app is terminated
    // If the app is terminated and a notification is received, Firebase displays it.
    // If the user taps the notification, this callback is triggered when the app starts.
    // We need to get the initial message.
    // FirebaseMessaging.instance.getInitialMessage().then((RemoteMessage? message) {
    //   if (message != null) {
    //     if (kDebugMode) {
    //       print('getInitialMessage: App was terminated and opened by notification');
    //       print('Message data: ${message.data}');
    //     }
    //     // TODO: Navigate to a specific screen based on message.data
    //   }
    // });

    // For background messages (not notification taps, but data-only messages when app is in background/terminated)
    // This requires a top-level or static function.
    // FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
  }
}

// This handler must be a top-level function (not a class method)
// It must not update UI directly.
// @pragma('vm:entry-point') // Promising an OOM-safe entry point for the isolate.
// Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
//   // If you're going to use other Firebase services in the background, like Firestore,
//   // make sure you call `initializeApp` before using other Firebase services.
//   // await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform); // Already initialized in main.dart usually
//   if (kDebugMode) {
//     print("Handling a background message: ${message.messageId}");
//     print('Message data: ${message.data}');
//   }
//   // Here you can perform background tasks, save data, etc.
//   // If you want to show a local notification for a data-only background message, you can do it here too.
// }

// TODO:
// - Implement onDidReceiveLocalNotification for older iOS versions if needed.
// - Implement onNotificationTapped for flutter_local_notifications.
// - Implement _firebaseMessagingBackgroundHandler if you need to handle data-only messages when the app is in background/terminated.
// - Implement logic to send FCM token to your backend server.
