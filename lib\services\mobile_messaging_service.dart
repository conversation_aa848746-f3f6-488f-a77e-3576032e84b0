import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart'; // For kDebugMode
import 'package:dalti/base/auth_utils.dart'; // Import auth_utils
import 'package:dalti/base/constant.dart'; // For prefsKeySessionId
import 'package:dalti/config/api_config.dart';
import 'package:dalti/config/network_config.dart';

// Message model
class MessageModel {
  final int? id; // Made optional as not present in lastMessage preview
  final String content;
  final DateTime createdAt;
  final MessageSender? sender; // Made optional
  final String? status; // Made optional, 'SENT', 'DELIVERED', 'READ'
  final String? senderName; // Added for lastMessage preview

  MessageModel({
    this.id,
    required this.content,
    required this.createdAt,
    this.sender,
    this.status,
    this.senderName,
  });

  factory MessageModel.fromJson(Map<String, dynamic> json) {
    return MessageModel(
      id: json['id'] as int?,
      content: json['content'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      // If 'sender' key exists and is a map, parse it as MessageSender
      // otherwise, it might be a lastMessage preview scenario.
      sender:
          json.containsKey('sender') && json['sender'] is Map<String, dynamic>
              ? MessageSender.fromJson(json['sender'] as Map<String, dynamic>)
              : null,
      status: json['status'] as String?,
      senderName:
          json.containsKey('senderName')
              ? json['senderName'] as String?
              : (json.containsKey('sender') &&
                      json['sender'] is Map<String, dynamic>
                  ? (json['sender'] as Map<String, dynamic>)['name'] as String?
                  : null),
    );
  }
}

class MessageSender {
  final String id;
  final String name;

  MessageSender({required this.id, required this.name});

  factory MessageSender.fromJson(Map<String, dynamic> json) {
    return MessageSender(
      id: json['id'] as String,
      name: json['name'] as String,
    );
  }
}

// Conversation model
class ConversationModel {
  final int id;
  final String? name; // Added
  final bool isGroup; // Added
  final String displayName; // Was 'title', now maps to 'displayName'
  final String? displayImage; // Added
  final MessageModel? lastMessage;
  final int unreadCount;
  final DateTime updatedAt;

  ConversationModel({
    required this.id,
    this.name,
    required this.isGroup,
    required this.displayName,
    this.displayImage,
    this.lastMessage,
    required this.unreadCount,
    required this.updatedAt,
  });

  factory ConversationModel.fromJson(Map<String, dynamic> json) {
    return ConversationModel(
      id: json['id'] as int,
      name: json['name'] as String?,
      isGroup: json['isGroup'] as bool? ?? false, // Default to false if null
      displayName:
          json['displayName'] as String? ?? 'Unknown', // Default if null
      displayImage: json['displayImage'] as String?,
      lastMessage:
          json['lastMessage'] != null
              ? MessageModel.fromJson(
                // This will use the updated MessageModel.fromJson
                json['lastMessage'] as Map<String, dynamic>,
              )
              : null,
      unreadCount: json['unreadCount'] as int? ?? 0, // Default to 0 if null
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }
}

class MobileMessagingService {
  Future<Map<String, String>> _getHeaders() async {
    String? sessionId = await getSessionId();
    if (sessionId == null || sessionId.isEmpty) {
      throw Exception('User not authenticated. Session ID is missing.');
    }
    return {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': 'Bearer $sessionId',
    };
  }

  // GET /api/auth/mobile/conversations
  Future<List<ConversationModel>> getConversations() async {
    final uri = Uri.parse(ApiConfig.mobileConversationsUrl);

    try {
      final headers = await _getHeaders();
      final response = await http.get(uri, headers: headers);
      print("Response body: ${response.body}");
      if (response.statusCode == 200) {
        if (kDebugMode) {
          print("Response body: ${response.body}");
        }
        final List<dynamic> responseData = jsonDecode(response.body);
        return responseData
            .map(
              (data) =>
                  ConversationModel.fromJson(data as Map<String, dynamic>),
            )
            .toList();
      } else if (response.statusCode == 401) {
        throw Exception('User not authenticated.');
      } else {
        if (kDebugMode) {
          print(
            "Error fetching conversations: ${response.statusCode} ${response.body}",
          );
        }
        throw Exception(
          'Failed to get conversations. Status: ${response.statusCode}',
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print("Exception fetching conversations: $e");
      }
      throw Exception('Failed to get conversations: $e');
    }
  }

  // GET /api/auth/mobile/messages/:conversationId
  Future<List<MessageModel>> getMessages(int conversationId) async {
    final uri = Uri.parse(ApiConfig.buildMobileMessagesUrl(conversationId));

    try {
      final headers = await _getHeaders();
      final response = await http.get(uri, headers: headers);

      if (response.statusCode == 200) {
        if (kDebugMode) {
          print("Response body: ${response.body}");
        }
        final List<dynamic> responseData = jsonDecode(response.body);
        return responseData
            .map((data) => MessageModel.fromJson(data as Map<String, dynamic>))
            .toList();
      } else if (response.statusCode == 401) {
        throw Exception('User not authenticated.');
      } else if (response.statusCode == 404) {
        throw Exception('Conversation not found.');
      } else {
        if (kDebugMode) {
          print(
            "Error fetching messages: ${response.statusCode} ${response.body}",
          );
        }
        throw Exception(
          'Failed to get messages. Status: ${response.statusCode}',
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print("Exception fetching messages: $e");
      }
      throw Exception('Failed to get messages: $e');
    }
  }

  // POST /api/auth/mobile/messages
  Future<MessageModel> sendMessage(int conversationId, String content) async {
    final uri = Uri.parse(ApiConfig.mobileMessagesUrl);
    final requestBody = jsonEncode({
      'conversationId': conversationId,
      'content': content,
    });

    try {
      final headers = await _getHeaders();
      final response = await http.post(
        uri,
        headers: headers,
        body: requestBody,
      );

      if (response.statusCode == 201) {
        if (kDebugMode) {
          print("Response body: ${response.body}");
        }
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        return MessageModel.fromJson(responseData);
      } else if (response.statusCode == 401) {
        throw Exception('User not authenticated.');
      } else if (response.statusCode == 404) {
        throw Exception('Conversation not found.');
      } else {
        if (kDebugMode) {
          print(
            "Error sending message: ${response.statusCode} ${response.body}",
          );
        }
        throw Exception(
          'Failed to send message. Status: ${response.statusCode}',
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print("Exception sending message: $e");
      }
      throw Exception('Failed to send message: $e');
    }
  }

  // POST /api/auth/mobile/messages/read
  Future<Map<String, dynamic>> markMessageAsRead(
    int messageId,
    int conversationId,
  ) async {
    final uri = Uri.parse(ApiConfig.mobileMarkMessageReadUrl);
    final requestBody = jsonEncode({
      'messageId': messageId,
      'conversationId': conversationId,
    });

    try {
      final headers = await _getHeaders();
      final response = await http.post(
        uri,
        headers: headers,
        body: requestBody,
      );

      if (response.statusCode == 200) {
        if (kDebugMode) {
          print("Response body: ${response.body}");
        }
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        return responseData;
      } else if (response.statusCode == 401) {
        throw Exception('User not authenticated.');
      } else if (response.statusCode == 404) {
        throw Exception('Message or conversation not found.');
      } else {
        if (kDebugMode) {
          print(
            "Error marking message as read: ${response.statusCode} ${response.body}",
          );
        }
        throw Exception(
          'Failed to mark message as read. Status: ${response.statusCode}',
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print("Exception marking message as read: $e");
      }
      throw Exception('Failed to mark message as read: $e');
    }
  }
}
