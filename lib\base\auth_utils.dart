import 'dart:async';
import 'dart:convert';

import 'package:dalti/app/routes/app_routes.dart';
import 'package:dalti/base/constant.dart';
import 'package:dalti/base/pref_data.dart';
import 'package:dalti/base/widget_utils.dart'; // For showCustomSnackBar
import 'package:dalti/config/api_config.dart';
import 'package:dalti/config/network_config.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

Future<void> clearAuthDataAndNavigateToLogin(BuildContext context) async {
  final prefs = await SharedPreferences.getInstance();
  await prefs.remove(prefsKeySessionId);
  await prefs.remove(prefsKeyUserProfile);
  await PrefData.setLogIn(false);

  // Navigate to LoginScreen and remove all previous routes
  Navigator.of(
    context,
  ).pushNamedAndRemoveUntil(Routes.loginRoute, (Route<dynamic> route) => false);
}

// Checks user session. If valid, fetches profile and returns it.
// If session is invalid or error occurs, navigates to login and returns null.
Future<Map<String, dynamic>?> checkUserSessionAndFetchProfile(
  BuildContext context,
) async {
  final prefs = await SharedPreferences.getInstance();
  final sessionId = prefs.getString(prefsKeySessionId);

  if (sessionId == null || sessionId.isEmpty) {
    // No session ID, treat as logged out
    // No need to show SnackBar, just navigate
    await clearAuthDataAndNavigateToLogin(context);
    return null;
  }

  try {
    final response = await http.get(
      Uri.parse(ApiConfig.userProfileUrl),
      headers: NetworkConfig.getAuthHeaders(sessionId),
    );

    if (response.statusCode == 200) {
      try {
        final userProfile = jsonDecode(response.body) as Map<String, dynamic>;
        await prefs.setString(prefsKeyUserProfile, response.body);
        // Ensure login status is true, though it should be if sessionId exists
        await PrefData.setLogIn(true);
        return userProfile;
      } catch (e) {
        // Failed to parse profile
        showCustomSnackBar(
          context,
          "Failed to process profile data. Please try logging in again.",
          CustomSnackBarType.error,
        );
        await clearAuthDataAndNavigateToLogin(context);
        return null;
      }
    } else if (response.statusCode == 401 || response.statusCode == 403) {
      // Unauthorized or Forbidden
      showCustomSnackBar(
        context,
        "Session expired or invalid. Please log in again.",
        CustomSnackBarType.warning,
      );
      await clearAuthDataAndNavigateToLogin(context);
      return null;
    } else {
      // Other HTTP errors
      String errorMessage =
          "Failed to verify session (Status: ${response.statusCode}).";
      try {
        final responseData = jsonDecode(response.body) as Map<String, dynamic>;
        errorMessage = responseData['message'] as String? ?? errorMessage;
      } catch (_) {
        /* Ignore if body isn't JSON */
      }

      showCustomSnackBar(context, errorMessage, CustomSnackBarType.error);
      // For other errors, we might not want to immediately log out,
      // but for a home screen check, failing to verify means we can't proceed.
      // Consider if navigation to login is always appropriate here or just return null.
      // For now, navigating to login to be safe if /auth/me fails for non-401/403 server errors.
      await clearAuthDataAndNavigateToLogin(context);
      return null;
    }
  } catch (e) {
    // Network or other exceptions
    showCustomSnackBar(
      context,
      "Network error or unable to reach server. Please check your connection.",
      CustomSnackBarType.error,
    );
    // Depending on app requirements, might navigate to login or allow retry
    // For HomeScreen check, if we can't verify, we should probably log out.
    await clearAuthDataAndNavigateToLogin(context);
    return null;
  }
}

// Function to retrieve only the session ID
Future<String?> getSessionId() async {
  final prefs = await SharedPreferences.getInstance();
  return prefs.getString(prefsKeySessionId);
}
