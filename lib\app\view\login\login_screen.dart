import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:dalti/base/resizer/fetch_pixels.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:dalti/l10n/app_localizations.dart';
import 'package:dalti/config/api_config.dart';
import 'package:dalti/config/network_config.dart';

import '../../../base/color_data.dart';
import '../../../base/constant.dart';
import '../../../base/pref_data.dart';
import '../../../base/widget_utils.dart';
import '../../routes/app_routes.dart';
import 'prefered_method.dart'; // Import for prefsKeyPreferredMethod

const String prefsKeySessionId = 'session_id';
const String prefsKeyUserProfile = 'user_profile';

class LoginScreen extends StatefulWidget {
  final String? preferredMethod; // Added to accept argument

  const LoginScreen({Key? key, this.preferredMethod}) : super(key: key);

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  TextEditingController emailOrPhoneController = TextEditingController();
  TextEditingController passwordController = TextEditingController();
  bool ispass = true;
  bool _isLoading = false;
  String? _currentPreferredMethod; // To store the method

  SharedPreferences? selection;

  @override
  void initState() {
    super.initState();
    _initializePreferredMethod();
    SharedPreferences.getInstance().then((SharedPreferences sp) {
      selection = sp;
      if (mounted) {
        setState(() {});
      }
    });
  }

  Future<void> _initializePreferredMethod() async {
    // Use widget.preferredMethod if passed, otherwise try to fetch from SharedPreferences
    if (widget.preferredMethod != null) {
      _currentPreferredMethod = widget.preferredMethod;
    } else {
      final prefs = await SharedPreferences.getInstance();
      _currentPreferredMethod = prefs.getString(prefsKeyPreferredMethod);
    }
    if (mounted) {
      setState(() {});
    }
  }

  void finishView() {
    // Navigate back to preferred method screen if no other screen to pop
    if (Navigator.canPop(context)) {
      Constant.backToPrev(context);
    } else {
      // If cannot pop, perhaps go to intro or preferred method selection
      Constant.sendToNext(context, Routes.preferredMethodRoute);
    }
  }

  Future<void> _fetchAndStoreUserProfile(String sessionId) async {
    try {
      final response = await http.get(
        Uri.parse(ApiConfig.userProfileUrl),
        headers: NetworkConfig.getAuthHeaders(sessionId),
      );

      if (mounted) {
        if (response.statusCode == 200) {
          final prefs = await SharedPreferences.getInstance();
          await prefs.setString(prefsKeyUserProfile, response.body);
          print("User profile fetched and saved to SharedPreferences.");
        } else {
          print(
            "Failed to fetch user profile: ${response.statusCode} - ${response.body}",
          );
        }
      }
    } catch (e) {
      print("Error fetching user profile: $e");
    }
  }

  Future<void> _performLogin() async {
    final localizations = AppLocalizations.of(context)!;
    setState(() {
      _isLoading = true;
    });

    String identifier;
    bool isEmailLogin = _currentPreferredMethod == 'email';

    if (isEmailLogin) {
      identifier = emailOrPhoneController.text.trim();
    } else {
      String countryCode =
          selection?.getString("code") ?? ""; // Ensure selection is initialized
      String localPhoneNumber = emailOrPhoneController.text.trim();
      if (localPhoneNumber.isEmpty) {
        showCustomSnackBar(
          context,
          localizations.errorPhoneNumberEmpty,
          CustomSnackBarType.warning,
          backgroundColor: daltiWarningYellow,
          textColor: daltiTextHeadline,
        );
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
        return;
      }
      identifier = countryCode + localPhoneNumber;
    }
    String password = passwordController.text.trim();

    if (identifier.isEmpty || password.isEmpty) {
      showCustomSnackBar(
        context,
        isEmailLogin
            ? localizations.errorEmailPasswordEmpty
            : localizations.errorPhonePasswordEmpty,
        CustomSnackBarType.warning,
        backgroundColor: daltiWarningYellow,
        textColor: daltiTextHeadline,
      );
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
      return;
    }

    try {
      final response = await http.post(
        Uri.parse(ApiConfig.loginUrl),
        headers: NetworkConfig.commonHeaders,
        body: jsonEncode({'identifier': identifier, 'password': password}),
      );

      Map<String, dynamic>? responseData;
      try {
        responseData = jsonDecode(response.body) as Map<String, dynamic>;
      } catch (e) {
        if (mounted) {
          showCustomSnackBar(
            context,
            localizations.errorLoginInvalidResponse(
              response.statusCode.toString(),
            ),
            CustomSnackBarType.error,
            backgroundColor: daltiErrorRed,
            textColor: daltiTextOnPrimary,
          );
          setState(() {
            _isLoading = false;
          });
        }
        return;
      }

      String message =
          responseData['message'] as String? ??
          "An unknown error occurred during login.";

      if (response.statusCode == 200) {
        String? sessionId = responseData['sessionId'] as String?;
        if (sessionId != null && sessionId.isNotEmpty) {
          final prefs = await SharedPreferences.getInstance();
          await prefs.setString(prefsKeySessionId, sessionId);
          await PrefData.setLogIn(true);

          await _fetchAndStoreUserProfile(sessionId);

          if (mounted) {
            showCustomSnackBar(
              context,
              localizations.loginSuccess,
              CustomSnackBarType.success,
              backgroundColor: daltiSuccessGreen,
              textColor: daltiTextOnPrimary,
            );
            Constant.sendToNext(context, Routes.homeScreenRoute);
          }
        } else {
          if (mounted) {
            showCustomSnackBar(
              context,
              localizations.errorLoginNoSessionId,
              CustomSnackBarType.error,
              backgroundColor: daltiErrorRed,
              textColor: daltiTextOnPrimary,
            );
          }
        }
      } else if (response.statusCode == 400 ||
          response.statusCode == 401 ||
          response.statusCode == 403 ||
          response.statusCode == 500) {
        if (mounted) {
          showCustomSnackBar(
            context,
            localizations.errorLoginWithMessageStatus(
              message,
              response.statusCode.toString(),
            ),
            CustomSnackBarType.error,
            backgroundColor: daltiErrorRed,
            textColor: daltiTextOnPrimary,
          );
        }
      } else {
        if (mounted) {
          showCustomSnackBar(
            context,
            localizations.errorLoginWithMessageStatus(
              message,
              response.statusCode.toString(),
            ),
            CustomSnackBarType.error,
            backgroundColor: daltiErrorRed,
            textColor: daltiTextOnPrimary,
          );
        }
      }
    } catch (e) {
      if (mounted) {
        showCustomSnackBar(
          context,
          localizations.errorLoginGeneric(e.toString()),
          CustomSnackBarType.error,
          backgroundColor: daltiErrorRed,
          textColor: daltiTextOnPrimary,
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    FetchPixels(context);
    final localizations = AppLocalizations.of(context)!;

    // If preferredMethod comes from arguments, update _currentPreferredMethod
    final args =
        ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
    if (args != null &&
        args['preferred_method'] != null &&
        _currentPreferredMethod != args['preferred_method']) {
      _currentPreferredMethod = args['preferred_method'] as String;
      // Clear fields if method changes
      emailOrPhoneController.clear();
      passwordController.clear();
    }

    return WillPopScope(
      onWillPop: () async {
        // When pressing back, go to the preferred method selection screen
        Constant.sendToNext(context, Routes.preferredMethodRoute);
        return false; // Prevent default back navigation
      },
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        backgroundColor: daltiBackground,
        body: SafeArea(
          child: Container(
            width: double.infinity,
            height: double.infinity,
            padding: EdgeInsets.symmetric(
              horizontal: FetchPixels.getDefaultHorSpace(context),
            ),
            child:
                _currentPreferredMethod == null
                    ? Center(
                      child: CircularProgressIndicator(color: daltiPrimary),
                    ) // Show loader if method not determined
                    : _buildLoginFormUI(localizations),
          ),
        ),
      ),
    );
  }

  Widget _buildLoginFormUI(AppLocalizations localizations) {
    bool isEmailLogin = _currentPreferredMethod == 'email';

    return ListView(
      primary: true,
      shrinkWrap: true,
      children: [
        getVerSpace(FetchPixels.getPixelHeight(20)),
        Align(
          alignment: Alignment.topLeft,
          child: IconButton(
            icon: Icon(Icons.arrow_back, color: daltiIconDefault),
            onPressed: () {
              // Go back to preferred method selection
              Constant.sendToNext(context, Routes.preferredMethodRoute);
            },
          ),
        ),
        getVerSpace(FetchPixels.getPixelHeight(20)),
        Align(
          alignment: Alignment.topCenter,
          child: getCustomFont(
            isEmailLogin
                ? localizations.loginWithEmailTitle
                : localizations.loginWithMobileTitle,
            24,
            daltiTextHeadline,
            1,
            fontWeight: FontWeight.w800,
          ),
        ),
        getVerSpace(FetchPixels.getPixelHeight(10)),
        Align(
          alignment: Alignment.topCenter,
          child: getCustomFont(
            localizations.loginSubtitle,
            16,
            daltiTextBody,
            1,
            fontWeight: FontWeight.w400,
          ),
        ),
        getVerSpace(FetchPixels.getPixelHeight(30)),
        if (isEmailLogin)
          getDefaultTextFiledWithLabel(
            context,
            localizations.emailLabel,
            emailOrPhoneController,
            daltiTextMuted,
            function: () {},
            height: FetchPixels.getPixelHeight(60),
            isEnable: false,
            withprefix: true,
            image: "message.svg",
          )
        else
          GestureDetector(
            onTap: () async {
              await Constant.sendToNext(context, Routes.selectCountryRoute);
              if (mounted) {
                setState(() {});
              }
            },
            child: getCountryTextField(
              context,
              localizations.phoneNumberLabel,
              emailOrPhoneController,
              daltiTextMuted,
              selection?.getString("code") ??
                  "+213", // Ensure 'selection' is not null
              height: FetchPixels.getPixelHeight(60),
              isEnable: false,
              image: selection?.getString("country") ?? "image_algeria.png",
              function: () {},
            ),
          ),
        getVerSpace(FetchPixels.getPixelHeight(20)),
        getDefaultTextFiledWithLabel(
          context,
          localizations.passwordLabel,
          passwordController,
          daltiTextMuted,
          function: () {},
          height: FetchPixels.getPixelHeight(60),
          isEnable: false,
          withprefix: true,
          image: "lock.svg",
          isPass: ispass,
          withSufix: true,
          suffiximage: "eye.svg",
          imagefunction: () {
            setState(() {
              ispass = !ispass;
            });
          },
        ),
        getVerSpace(FetchPixels.getPixelHeight(19)),
        Align(
          alignment: Alignment.topRight,
          child: GestureDetector(
            onTap: () {
              // Navigate directly to new password reset flow
              Constant.sendToNext(context, Routes.passwordResetRequestRoute);
            },
            child: getCustomFont(
              localizations.forgotPasswordButton,
              16,
              daltiPrimary,
              1,
              fontWeight: FontWeight.w800,
            ),
          ),
        ),
        getVerSpace(FetchPixels.getPixelHeight(49)),
        _isLoading
            ? Center(child: CircularProgressIndicator(color: daltiPrimary))
            : getButton(
              context,
              daltiPrimary,
              localizations.loginButton,
              daltiTextOnPrimary,
              _performLogin,
              18,
              weight: FontWeight.w600,
              buttonHeight: FetchPixels.getPixelHeight(60),
              borderRadius: BorderRadius.circular(
                FetchPixels.getPixelHeight(15),
              ),
            ),
        getVerSpace(FetchPixels.getPixelHeight(30)),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            getCustomFont(
              localizations.dontHaveAccountText,
              14,
              daltiTextMuted,
              1,
              fontWeight: FontWeight.w400,
            ),
            GestureDetector(
              onTap: () {
                // Pass the preferred method to signup screen
                Constant.sendToNext(
                  context,
                  Routes.signupRoute,
                  arguments: {'preferred_method': _currentPreferredMethod},
                );
              },
              child: getCustomFont(
                localizations.signUpButtonLoginScreen,
                16,
                daltiPrimary,
                1,
                fontWeight: FontWeight.w800,
              ),
            ),
          ],
        ),
        getVerSpace(
          FetchPixels.getPixelHeight(20),
        ), // Space before login button
      ],
    );
  }
}
