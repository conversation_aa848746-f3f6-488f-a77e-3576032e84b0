import 'dart:convert';
import 'package:dalti/base/auth_utils.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:dalti/config/api_config.dart';
import 'package:dalti/config/network_config.dart';

class UserService {
  Future<String> _getAuthToken() async {
    String? sessionId = await getSessionId();
    if (sessionId == null || sessionId.isEmpty) {
      throw Exception('Authentication token not found. Please log in.');
    }
    return sessionId;
  }

  Future<bool> updatePreferredLanguage(String languageCode) async {
    final token = await _getAuthToken();

    final Uri url = Uri.parse(ApiConfig.updatePreferredLanguageUrl);

    final Map<String, String> headers = {
      'Content-Type': 'application/json; charset=UTF-8',
      'Authorization': 'Bearer $token',
    };

    final Map<String, String> body = {
      'preferedLanguage': languageCode.toUpperCase(), // Ensure it's EN, FR, AR
    };

    try {
      final response = await http.post(
        url,
        headers: headers,
        body: jsonEncode(body),
      );

      if (response.statusCode == 200) {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('language_code', languageCode.toLowerCase());
        // Successfully updated
        print('Preferred language updated to $languageCode successfully.');
        return true;
      } else {
        // Handle other status codes, e.g., 401 for unauthorized, 400 for bad request
        print(
          'Failed to update preferred language. Status: ${response.statusCode}, Body: ${response.body}',
        );
        throw Exception(
          'Failed to update preferred language. Status: ${response.statusCode}',
        );
      }
    } catch (e) {
      print('Error updating preferred language: $e');
      throw Exception('Error updating preferred language: $e');
    }
  }

  // Placeholder for fetching user profile data
  Future<Map<String, dynamic>> getUserProfile() async {
    // TODO: Implement API call to fetch user profile
    await Future.delayed(const Duration(seconds: 1)); // Simulate network call
    return {
      'name': 'John Doe',
      'email': '<EMAIL>',
      'preferedLanguage': 'EN',
    };
  }

  // Placeholder for updating user profile data
  Future<bool> updateUserProfile(Map<String, dynamic> profileData) async {
    // TODO: Implement API call to update user profile
    await Future.delayed(const Duration(seconds: 1)); // Simulate network call
    print('Profile data to update: $profileData');
    return true;
  }

  // Placeholder for fetching user settings
  Future<Map<String, dynamic>> getUserSettings() async {
    // TODO: Implement API call to fetch user settings
    await Future.delayed(const Duration(seconds: 1)); // Simulate network call
    return {'notificationsEnabled': true, 'darkMode': false};
  }

  // Placeholder for updating user settings
  Future<bool> updateUserSettings(Map<String, dynamic> settingsData) async {
    // TODO: Implement API call to update user settings
    await Future.delayed(const Duration(seconds: 1)); // Simulate network call
    print('Settings data to update: $settingsData');
    return true;
  }
}
