import 'package:dalti/app/data/data_file.dart';
import 'package:dalti/app/models/model_category.dart';
import 'package:dalti/app/utils/category_utils.dart';

import 'package:dalti/app/models/model_popular_service.dart';
import 'package:dalti/app/routes/app_routes.dart';
import 'package:dalti/base/color_data.dart';
import 'package:dalti/base/constant.dart';
import 'package:dalti/base/resizer/fetch_pixels.dart';
import 'package:dalti/base/widget_utils.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:dalti/l10n/app_localizations.dart';
import 'package:dalti/services/advertisement_service.dart';
import 'package:dalti/app/models/model_appointment.dart';
import 'package:dalti/app/models/model_doctor.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:dalti/base/auth_utils.dart';
import 'package:dalti/config/api_config.dart';
import 'package:dalti/config/network_config.dart';

class TabHome extends StatefulWidget {
  const TabHome({Key? key}) : super(key: key);

  @override
  State<TabHome> createState() => _TabHomeState();
}

class _TabHomeState extends State<TabHome> {
  TextEditingController searchController = TextEditingController();
  Future<List<ModelCategory>>? _categoriesFuture;
  List<ModelPopularService> popularServiceLists = DataFile.popularServiceList;
  ValueNotifier selectedPage = ValueNotifier(0);
  final _controller = PageController();
  SharedPreferences? selection;

  // Advertisement state
  List<Advertisement> _advertisements = [];
  bool _isLoadingAds = true;
  final AdvertisementService _advertisementService = AdvertisementService();

  // Upcoming appointments state
  List<ModelAppointment> _upcomingAppointments = [];
  bool _isLoadingUpcoming = true;

  // Favorite providers state
  List<ModelDoctor> _favoriteProviders = [];
  bool _isLoadingFavorites = true;

  @override
  void initState() {
    super.initState();
    _categoriesFuture = fetchCategories();
    _loadAdvertisements();
    _loadUpcomingAppointments();
    _loadFavoriteProviders();

    SharedPreferences.getInstance().then((SharedPreferences sp) {
      selection = sp;
      if (mounted) {
        setState(() {});
      }
    });
  }

  Future<void> _loadAdvertisements() async {
    try {
      final advertisements = await _advertisementService.getAdvertisements();
      print('DEBUG: Loaded ${advertisements.length} advertisements');
      for (int i = 0; i < advertisements.length; i++) {
        print(
          'DEBUG: Ad $i - Title: ${advertisements[i].title}, Subtitle: ${advertisements[i].subtitle}',
        );
      }
      setState(() {
        _advertisements = advertisements;
        _isLoadingAds = false;
      });
    } catch (e) {
      print('Error loading advertisements: $e');
      setState(() {
        _advertisements = [];
        _isLoadingAds = false;
      });
    }
  }

  Future<void> _loadUpcomingAppointments() async {
    try {
      String? sessionId = await getSessionId();
      if (sessionId == null) {
        setState(() {
          _upcomingAppointments = [];
          _isLoadingUpcoming = false;
        });
        return;
      }

      final response = await http.get(
        Uri.parse(ApiConfig.customerAppointmentsUrl),
        headers: NetworkConfig.getAuthHeaders(sessionId),
      );

      if (response.statusCode == 200) {
        final List<dynamic> responseData = jsonDecode(response.body);
        final allAppointments =
            responseData
                .map(
                  (data) =>
                      ModelAppointment.fromJson(data as Map<String, dynamic>),
                )
                .toList();

        // Filter for upcoming appointments (confirmed and scheduled)
        final now = DateTime.now();
        final upcoming =
            allAppointments
                .where(
                  (appointment) =>
                      appointment.expectedAppointmentStartTime.isAfter(now) &&
                      (appointment.status == 'confirmed' ||
                          appointment.status == 'scheduled'),
                )
                .take(5) // Limit to 5 upcoming appointments
                .toList();

        setState(() {
          _upcomingAppointments = upcoming;
          _isLoadingUpcoming = false;
        });
      } else {
        setState(() {
          _upcomingAppointments = [];
          _isLoadingUpcoming = false;
        });
      }
    } catch (e) {
      print('Error loading upcoming appointments: $e');
      setState(() {
        _upcomingAppointments = [];
        _isLoadingUpcoming = false;
      });
    }
  }

  Future<void> _loadFavoriteProviders() async {
    try {
      String? sessionId = await getSessionId();
      if (sessionId == null) {
        setState(() {
          _favoriteProviders = [];
          _isLoadingFavorites = false;
        });
        return;
      }

      // First, get completed appointments to find providers
      const String apiUrl =
          "https://dapi-test.adscloud.org:8443/api/auth/customer/appointments";
      final response = await http.get(
        Uri.parse(apiUrl),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': 'Bearer $sessionId',
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> responseData = jsonDecode(response.body);
        final allAppointments =
            responseData
                .map(
                  (data) =>
                      ModelAppointment.fromJson(data as Map<String, dynamic>),
                )
                .toList();

        // Get unique provider IDs from completed appointments
        final completedAppointments =
            allAppointments
                .where((appointment) => appointment.status == 'completed')
                .toList();

        final Set<int> providerIds =
            completedAppointments
                .map((appointment) => appointment.place.id)
                .where((id) => id != null)
                .cast<int>()
                .toSet();

        // For now, we'll create mock favorite providers based on completed appointments
        // In a real implementation, you'd fetch actual provider details
        final List<ModelDoctor> favorites = [];
        for (int providerId in providerIds.take(5)) {
          // Create a mock provider - in real implementation, fetch from provider API
          final appointment = completedAppointments.firstWhere(
            (apt) => apt.place.id == providerId,
          );

          favorites.add(
            ModelDoctor(
              id: providerId,
              imageAsset: "barber.png", // Default image
              name: appointment.place.name ?? "Unknown Provider",
              specialization: appointment.service.title ?? "Service",
              hospital: appointment.place.name ?? "Unknown Location",
              experience: 5,
              fees: 50.0,
              rating: 4.5,
              reviewCount: 10,
              imageBackgroundColor: daltiPrimary,
              sProvidingPlaceId: providerId,
              services: [],
              queues: [],
            ),
          );
        }

        setState(() {
          _favoriteProviders = favorites;
          _isLoadingFavorites = false;
        });
      } else {
        setState(() {
          _favoriteProviders = [];
          _isLoadingFavorites = false;
        });
      }
    } catch (e) {
      print('Error loading favorite providers: $e');
      setState(() {
        _favoriteProviders = [];
        _isLoadingFavorites = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    return Container(
      color: appBackground,
      child: Column(
        children: [
          getVerSpace(FetchPixels.getPixelHeight(21)),
          getPaddingWidget(
            EdgeInsets.symmetric(horizontal: FetchPixels.getPixelWidth(20)),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                getSvgImage(
                  "menu.svg",
                  height: FetchPixels.getPixelHeight(24),
                  width: FetchPixels.getPixelHeight(24),
                  color: appMuted,
                ),
                Row(
                  children: [
                    getSvgImage("location.svg", color: appMuted),
                    getHorSpace(FetchPixels.getPixelWidth(4)),
                    getCustomFont(
                      selection?.getString("address") ??
                          localizations.setLocationDefault,
                      14,
                      appTextBody,
                      1,
                      fontWeight: FontWeight.w400,
                    ),
                  ],
                ),
                GestureDetector(
                  onTap: () {
                    Constant.sendToNext(context, Routes.notificationRoutes);
                  },
                  child: getSvgImage(
                    "notification.svg",
                    height: FetchPixels.getPixelHeight(24),
                    width: FetchPixels.getPixelHeight(24),
                    color: appMuted,
                  ),
                ),
              ],
            ),
          ),
          getVerSpace(FetchPixels.getPixelHeight(32)),
          getPaddingWidget(
            EdgeInsets.symmetric(horizontal: FetchPixels.getPixelWidth(20)),
            TextField(
              controller: searchController,
              onTap: () {
                Constant.sendToNext(
                  context,
                  Routes.searchRoute,
                  arguments: {'query': searchController.text},
                );
              },
              decoration: InputDecoration(
                hintText: localizations.searchHintText,
                isDense: true,
                border: InputBorder.none,
                prefixIcon: Padding(
                  padding: EdgeInsets.only(
                    right: FetchPixels.getPixelWidth(18),
                    left: FetchPixels.getPixelWidth(19),
                  ),
                  child: getSvgImage(
                    "search.svg",
                    height: FetchPixels.getPixelHeight(24),
                    width: FetchPixels.getPixelHeight(24),
                  ),
                ),
                filled: true,
                fillColor: Colors.white,
                contentPadding: EdgeInsets.symmetric(
                  vertical: FetchPixels.getPixelHeight(10),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(
                    FetchPixels.getPixelHeight(12),
                  ),
                  borderSide: BorderSide(color: Colors.black12, width: 0.0),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(
                    FetchPixels.getPixelHeight(12),
                  ),
                  borderSide: BorderSide(color: daltiPrimary, width: 1.0),
                ),
              ),
              style: TextStyle(
                color: Colors.black,
                fontWeight: FontWeight.w400,
                fontSize: 16,
                fontFamily: Constant.fontsFamily,
              ),
            ),
          ),
          getVerSpace(FetchPixels.getPixelHeight(20)),
          Expanded(
            flex: 1,
            child: ListView(
              primary: true,
              physics: const BouncingScrollPhysics(),
              scrollDirection: Axis.vertical,
              children: [
                SizedBox(
                  height: FetchPixels.getPixelHeight(184),
                  child:
                      _isLoadingAds
                          ? Center(
                            child: CircularProgressIndicator(
                              color: daltiPrimary,
                            ),
                          )
                          : PageView.builder(
                            controller: _controller,
                            onPageChanged: (value) {
                              selectedPage.value = value;
                            },
                            itemCount:
                                _advertisements.isNotEmpty
                                    ? _advertisements.length
                                    : 3,
                            itemBuilder: (context, index) {
                              // Get advertisement data or use fallback
                              Advertisement? ad =
                                  _advertisements.isNotEmpty &&
                                          index < _advertisements.length
                                      ? _advertisements[index]
                                      : null;

                              print(
                                'DEBUG: Banner $index - Ad: ${ad?.title ?? "null"}, Using fallback: ${ad == null}',
                              );
                              return Stack(
                                alignment: Alignment.center,
                                children: [
                                  Container(
                                    width: FetchPixels.getPixelWidth(374),
                                    decoration: BoxDecoration(
                                      color: daltiPrimary,
                                      borderRadius: BorderRadius.circular(
                                        FetchPixels.getPixelHeight(20),
                                      ),
                                    ),
                                    alignment: Alignment.center,
                                  ),
                                  Positioned(
                                    child: SizedBox(
                                      height: FetchPixels.getPixelHeight(180),
                                      width: FetchPixels.getPixelWidth(374),
                                      child: Row(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.end,
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          getPaddingWidget(
                                            EdgeInsets.only(
                                              left: FetchPixels.getPixelWidth(
                                                20,
                                              ),
                                              top: FetchPixels.getPixelHeight(
                                                20,
                                              ),
                                              bottom:
                                                  FetchPixels.getPixelHeight(
                                                    20,
                                                  ),
                                            ),
                                            Column(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.start,
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                SizedBox(
                                                  width:
                                                      FetchPixels.getPixelWidth(
                                                        150,
                                                      ),
                                                  child: getMultilineCustomFont(
                                                    ad?.title ??
                                                        localizations
                                                            .homeBannerTitle,
                                                    20,
                                                    Colors.white,
                                                    fontWeight: FontWeight.w800,
                                                    txtHeight:
                                                        FetchPixels.getPixelHeight(
                                                          1.3,
                                                        ),
                                                  ),
                                                ),
                                                getVerSpace(
                                                  FetchPixels.getPixelHeight(6),
                                                ),
                                                getCustomFont(
                                                  ad?.subtitle ??
                                                      localizations
                                                          .homeBannerSubtitle,
                                                  14,
                                                  daltiPrimaryLight,
                                                  1,
                                                  fontWeight: FontWeight.w400,
                                                ),
                                                const Spacer(),
                                                getButton(
                                                  context,
                                                  appPrimary,
                                                  ad?.callToActionText ??
                                                      localizations
                                                          .bookNowButton,
                                                  Colors.white,
                                                  () {
                                                    // TODO: Handle call-to-action link if needed
                                                    // ad?.callToActionLink
                                                  },
                                                  14,
                                                  weight: FontWeight.w600,
                                                  buttonWidth:
                                                      FetchPixels.getPixelWidth(
                                                        108,
                                                      ),
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                        FetchPixels.getPixelHeight(
                                                          10,
                                                        ),
                                                      ),
                                                  insetsGeometrypadding:
                                                      EdgeInsets.symmetric(
                                                        vertical:
                                                            FetchPixels.getPixelHeight(
                                                              12,
                                                            ),
                                                      ),
                                                ),
                                              ],
                                            ),
                                          ),
                                          Container(
                                            margin: EdgeInsets.only(
                                              right: FetchPixels.getPixelWidth(
                                                21,
                                              ),
                                            ),
                                            height: FetchPixels.getPixelHeight(
                                              175,
                                            ),
                                            width: FetchPixels.getPixelHeight(
                                              142,
                                            ),
                                            color: Colors.transparent,
                                            child: getAssetImage(
                                              "washer.png",
                                              FetchPixels.getPixelHeight(142),
                                              FetchPixels.getPixelHeight(175),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              );
                            },
                          ),
                ),
                getVerSpace(FetchPixels.getPixelHeight(14)),
                Align(
                  alignment: Alignment.topCenter,
                  child: ValueListenableBuilder(
                    valueListenable: selectedPage,
                    builder: (context, value, child) {
                      return Container(
                        height: FetchPixels.getPixelHeight(8),
                        width: FetchPixels.getPixelWidth(44),
                        alignment: Alignment.center,
                        child: ListView.builder(
                          primary: false,
                          itemCount:
                              _advertisements.isNotEmpty
                                  ? _advertisements.length
                                  : 3,
                          scrollDirection: Axis.horizontal,
                          itemBuilder: (context, index) {
                            return getPaddingWidget(
                              EdgeInsets.only(
                                right: FetchPixels.getPixelWidth(10),
                              ),
                              getAssetImage(
                                "dot.png",
                                FetchPixels.getPixelHeight(8),
                                FetchPixels.getPixelHeight(8),
                                color:
                                    selectedPage.value == index
                                        ? appPrimary
                                        : appMuted,
                              ),
                            );
                          },
                        ),
                      );
                    },
                  ),
                ),
                getVerSpace(FetchPixels.getPixelHeight(24)),

                getPaddingWidget(
                  EdgeInsets.symmetric(
                    horizontal: FetchPixels.getPixelWidth(20),
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      getCustomFont(
                        localizations.categoriesTitle,
                        18,
                        appTextHeadline,
                        1,
                        fontWeight: FontWeight.w800,
                      ),
                      GestureDetector(
                        onTap: () {
                          Constant.sendToNext(context, Routes.categoryRoute);
                        },
                        child: getCustomFont(
                          localizations.seeAllButton,
                          16,
                          appPrimary,
                          1,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
                getVerSpace(FetchPixels.getPixelHeight(16)),
                FutureBuilder<List<ModelCategory>>(
                  future: _categoriesFuture,
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return SizedBox(
                        height: FetchPixels.getPixelHeight(106),
                        child: Center(
                          child: CircularProgressIndicator(color: appPrimary),
                        ),
                      );
                    }
                    if (snapshot.hasError) {
                      return SizedBox(
                        height: FetchPixels.getPixelHeight(106),
                        child: Center(
                          child: Text(
                            localizations.errorLoadingCategories(
                              snapshot.error.toString(),
                            ),
                            style: TextStyle(color: appPrimary),
                          ),
                        ),
                      );
                    }
                    if (!snapshot.hasData || snapshot.data!.isEmpty) {
                      return SizedBox(
                        height: FetchPixels.getPixelHeight(106),
                        child: Center(
                          child: Text(
                            localizations.noCategoriesFound,
                            style: TextStyle(color: appTextMuted),
                          ),
                        ),
                      );
                    }
                    final allCategories = snapshot.data!;
                    final topLevelCategories = ModelCategory.buildHierarchy(
                      allCategories,
                    );
                    final limitedParentCategories =
                        topLevelCategories.take(8).toList();

                    if (limitedParentCategories.isEmpty) {
                      return SizedBox(
                        height: FetchPixels.getPixelHeight(106),
                        child: Center(
                          child: Text(
                            localizations.noParentCategoriesFound,
                            style: TextStyle(color: appTextMuted),
                          ),
                        ),
                      );
                    }
                    return categoryView(limitedParentCategories);
                  },
                ),
                getVerSpace(FetchPixels.getPixelHeight(30)),
                // Upcoming Appointments Section
                _buildUpcomingAppointmentsSection(localizations),

                // Favorite Providers Section
                _buildFavoriteProvidersSection(localizations),
              ],
            ),
          ),
        ],
      ),
    );
  }

  SizedBox categoryView(List<ModelCategory> categoryLists) {
    return SizedBox(
      height: FetchPixels.getPixelHeight(125),
      child: ListView.builder(
        primary: false,
        shrinkWrap: true,
        scrollDirection: Axis.horizontal,
        itemCount: categoryLists.length,
        padding: EdgeInsets.symmetric(
          horizontal: FetchPixels.getPixelWidth(12),
        ),
        itemBuilder: (context, index) {
          ModelCategory modelCategory = categoryLists[index];
          return Padding(
            padding: EdgeInsets.only(right: FetchPixels.getPixelWidth(20)),
            child: GestureDetector(
              onTap: () {
                Constant.sendToNext(
                  context,
                  Routes.categoryRoute,
                  arguments: modelCategory,
                );
              },
              child: SizedBox(
                width: FetchPixels.getPixelWidth(85),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      height: FetchPixels.getPixelHeight(70),
                      width: FetchPixels.getPixelHeight(70),
                      decoration: BoxDecoration(
                        color: daltiPrimary.withOpacity(0.1),
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.08),
                            spreadRadius: 1,
                            blurRadius: 10,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      alignment: Alignment.center,
                      child: Icon(
                        Icons.category_outlined,
                        color: daltiPrimary,
                        size: FetchPixels.getPixelHeight(30),
                      ),
                    ),
                    getVerSpace(FetchPixels.getPixelHeight(10)),
                    getCustomFont(
                      modelCategory.title,
                      13,
                      appTextHeadline,
                      1,
                      fontWeight: FontWeight.w600,
                      textAlign: TextAlign.center,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildUpcomingAppointmentsSection(AppLocalizations localizations) {
    return Column(
      children: [
        // Header with title and "View All"
        getPaddingWidget(
          EdgeInsets.symmetric(horizontal: FetchPixels.getPixelWidth(20)),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  getCustomFont(
                    "Today's Appointments", // TODO: Add to localizations
                    18,
                    appTextHeadline,
                    1,
                    fontWeight: FontWeight.w800,
                  ),
                  getVerSpace(FetchPixels.getPixelHeight(4)),
                  Row(
                    children: [
                      Container(
                        width: FetchPixels.getPixelHeight(8),
                        height: FetchPixels.getPixelHeight(8),
                        decoration: BoxDecoration(
                          color: Colors.green,
                          shape: BoxShape.circle,
                        ),
                      ),
                      getHorSpace(FetchPixels.getPixelWidth(6)),
                      getCustomFont(
                        "Updated 14s ago",
                        12,
                        appTextMuted,
                        1,
                        fontWeight: FontWeight.w400,
                      ),
                    ],
                  ),
                ],
              ),
              Row(
                children: [
                  Icon(
                    Icons.refresh,
                    color: appTextMuted,
                    size: FetchPixels.getPixelHeight(16),
                  ),
                  getHorSpace(FetchPixels.getPixelWidth(8)),
                  getCustomFont(
                    "View All",
                    14,
                    appPrimary,
                    1,
                    fontWeight: FontWeight.w600,
                  ),
                ],
              ),
            ],
          ),
        ),
        getVerSpace(FetchPixels.getPixelHeight(16)),

        // Content
        _isLoadingUpcoming
            ? Container(
              height: FetchPixels.getPixelHeight(120),
              child: Center(
                child: CircularProgressIndicator(color: daltiPrimary),
              ),
            )
            : _upcomingAppointments.isEmpty
            ? Container(
              height: FetchPixels.getPixelHeight(120),
              child: Center(
                child: getCustomFont(
                  "No upcoming appointments",
                  14,
                  appTextMuted,
                  1,
                ),
              ),
            )
            : _buildAppointmentsList(),

        getVerSpace(FetchPixels.getPixelHeight(24)),
      ],
    );
  }

  Widget _buildAppointmentsList() {
    return getPaddingWidget(
      EdgeInsets.symmetric(horizontal: FetchPixels.getPixelWidth(20)),
      Column(
        children: [
          // Next Appointment Card (highlighted)
          if (_upcomingAppointments.isNotEmpty) ...[
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(FetchPixels.getPixelHeight(16)),
              decoration: BoxDecoration(
                color: daltiPrimary.withValues(alpha: 0.08),
                borderRadius: BorderRadius.circular(
                  FetchPixels.getPixelHeight(12),
                ),
                border: Border.all(
                  color: daltiPrimary.withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        width: FetchPixels.getPixelHeight(8),
                        height: FetchPixels.getPixelHeight(8),
                        decoration: BoxDecoration(
                          color: daltiPrimary,
                          shape: BoxShape.circle,
                        ),
                      ),
                      getHorSpace(FetchPixels.getPixelWidth(8)),
                      getCustomFont(
                        "Next Appointment",
                        12,
                        daltiPrimary,
                        1,
                        fontWeight: FontWeight.w600,
                      ),
                    ],
                  ),
                  getVerSpace(FetchPixels.getPixelHeight(12)),
                  _buildNextAppointmentContent(_upcomingAppointments.first),
                ],
              ),
            ),
            getVerSpace(FetchPixels.getPixelHeight(16)),
          ],

          // List of other appointments
          if (_upcomingAppointments.length > 1) ...[
            ..._upcomingAppointments
                .skip(1)
                .map((appointment) => _buildAppointmentListItem(appointment))
                .toList(),
          ],
        ],
      ),
    );
  }

  Widget _buildNextAppointmentContent(ModelAppointment appointment) {
    final DateTime appointmentTime = appointment.expectedAppointmentStartTime;
    final String formattedTime =
        "${appointmentTime.hour.toString().padLeft(2, '0')}:${appointmentTime.minute.toString().padLeft(2, '0')}";

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              getCustomFont(
                "Provider Name", // TODO: Get actual provider name
                16,
                appTextHeadline,
                1,
                fontWeight: FontWeight.w700,
              ),
              getVerSpace(FetchPixels.getPixelHeight(4)),
              getCustomFont(
                appointment.service.title,
                14,
                appTextBody,
                1,
                fontWeight: FontWeight.w500,
              ),
            ],
          ),
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            getCustomFont(
              formattedTime,
              16,
              appTextHeadline,
              1,
              fontWeight: FontWeight.w700,
            ),
            getVerSpace(FetchPixels.getPixelHeight(4)),
            getCustomFont(
              "${appointment.serviceDuration ?? 30} min",
              12,
              appTextMuted,
              1,
              fontWeight: FontWeight.w500,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildAppointmentListItem(ModelAppointment appointment) {
    final DateTime appointmentTime = appointment.expectedAppointmentStartTime;
    final String formattedTime =
        "${appointmentTime.hour.toString().padLeft(2, '0')}:${appointmentTime.minute.toString().padLeft(2, '0')}";

    // Get status color
    Color statusColor = Colors.green;
    String statusText = "Confirmed";
    if (appointment.status == 'scheduled') {
      statusColor = Colors.orange;
      statusText = "Scheduled";
    }

    return Container(
      margin: EdgeInsets.only(bottom: FetchPixels.getPixelHeight(12)),
      child: Row(
        children: [
          // Avatar
          Container(
            width: FetchPixels.getPixelHeight(40),
            height: FetchPixels.getPixelHeight(40),
            decoration: BoxDecoration(
              color: daltiPrimary,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: getCustomFont(
                "ZS", // TODO: Get actual initials
                14,
                Colors.white,
                1,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          getHorSpace(FetchPixels.getPixelWidth(12)),

          // Content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                getCustomFont(
                  "Provider Name", // TODO: Get actual provider name
                  14,
                  appTextHeadline,
                  1,
                  fontWeight: FontWeight.w600,
                ),
                getVerSpace(FetchPixels.getPixelHeight(2)),
                getCustomFont(
                  appointment.service.title,
                  12,
                  appTextBody,
                  1,
                  fontWeight: FontWeight.w400,
                ),
              ],
            ),
          ),

          // Time and Status
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              getCustomFont(
                formattedTime,
                14,
                appTextHeadline,
                1,
                fontWeight: FontWeight.w600,
              ),
              getVerSpace(FetchPixels.getPixelHeight(2)),
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: FetchPixels.getPixelWidth(8),
                  vertical: FetchPixels.getPixelHeight(2),
                ),
                decoration: BoxDecoration(
                  color: statusColor,
                  borderRadius: BorderRadius.circular(
                    FetchPixels.getPixelHeight(8),
                  ),
                ),
                child: getCustomFont(
                  statusText,
                  10,
                  Colors.white,
                  1,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFavoriteProvidersSection(AppLocalizations localizations) {
    return Column(
      children: [
        getPaddingWidget(
          EdgeInsets.symmetric(horizontal: FetchPixels.getPixelWidth(20)),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              getCustomFont(
                "Favorite Providers", // TODO: Add to localizations
                18,
                appTextHeadline,
                1,
                fontWeight: FontWeight.w800,
              ),
              GestureDetector(
                onTap: () {
                  // Navigate to search/providers
                  Constant.sendToNext(context, Routes.searchRoute);
                },
                child: getCustomFont(
                  localizations.seeAllButton,
                  16,
                  appPrimary,
                  1,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
        getVerSpace(FetchPixels.getPixelHeight(16)),
        SizedBox(
          height: FetchPixels.getPixelHeight(200),
          child:
              _isLoadingFavorites
                  ? Center(
                    child: CircularProgressIndicator(color: daltiPrimary),
                  )
                  : _favoriteProviders.isEmpty
                  ? Center(
                    child: getCustomFont(
                      "No favorite providers yet",
                      14,
                      appTextMuted,
                      1,
                    ),
                  )
                  : ListView.builder(
                    scrollDirection: Axis.horizontal,
                    padding: EdgeInsets.symmetric(
                      horizontal: FetchPixels.getPixelWidth(20),
                    ),
                    itemCount: _favoriteProviders.length,
                    itemBuilder: (context, index) {
                      final provider = _favoriteProviders[index];
                      return _buildFavoriteProviderCard(provider);
                    },
                  ),
        ),
        getVerSpace(FetchPixels.getPixelHeight(24)),
      ],
    );
  }

  Widget _buildFavoriteProviderCard(ModelDoctor provider) {
    return GestureDetector(
      onTap: () {
        Constant.sendToNext(context, Routes.detailRoute, arguments: provider);
      },
      child: Container(
        width: FetchPixels.getPixelWidth(160),
        margin: EdgeInsets.only(right: FetchPixels.getPixelWidth(16)),
        padding: EdgeInsets.all(FetchPixels.getPixelHeight(16)),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(FetchPixels.getPixelHeight(16)),
          border: Border.all(
            color: Colors.grey.withValues(alpha: 0.1),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.06),
              blurRadius: 15,
              offset: const Offset(0, 5),
              spreadRadius: 0,
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Provider Image/Avatar
            Container(
              width: FetchPixels.getPixelHeight(70),
              height: FetchPixels.getPixelHeight(70),
              decoration: BoxDecoration(
                color: provider.imageBackgroundColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(
                  FetchPixels.getPixelHeight(12),
                ),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(
                  FetchPixels.getPixelHeight(12),
                ),
                child: getAssetImage(
                  provider.imageAsset,
                  FetchPixels.getPixelHeight(50),
                  FetchPixels.getPixelHeight(50),
                ),
              ),
            ),
            getVerSpace(FetchPixels.getPixelHeight(8)),

            // Provider Name
            getCustomFont(
              provider.name,
              13,
              appTextHeadline,
              2,
              fontWeight: FontWeight.w700,
              textAlign: TextAlign.center,
            ),
            getVerSpace(FetchPixels.getPixelHeight(2)),

            // Specialization
            getCustomFont(
              provider.specialization,
              11,
              appTextBody,
              2,
              textAlign: TextAlign.center,
              fontWeight: FontWeight.w500,
            ),
            getVerSpace(FetchPixels.getPixelHeight(6)),

            // Rating
            Container(
              padding: EdgeInsets.symmetric(
                horizontal: FetchPixels.getPixelWidth(6),
                vertical: FetchPixels.getPixelHeight(2),
              ),
              decoration: BoxDecoration(
                color: Colors.amber.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(
                  FetchPixels.getPixelHeight(10),
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.star,
                    color: Colors.amber,
                    size: FetchPixels.getPixelHeight(12),
                  ),
                  getHorSpace(FetchPixels.getPixelWidth(2)),
                  getCustomFont(
                    provider.rating.toString(),
                    11,
                    appTextHeadline,
                    1,
                    fontWeight: FontWeight.w600,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
