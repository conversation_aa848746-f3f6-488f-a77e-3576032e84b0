# DALTI Mobile Application - Technical Specification

## Table of Contents
1. [App Overview & Architecture](#app-overview--architecture)
2. [API Specifications](#api-specifications)
3. [External Service Integrations](#external-service-integrations)
4. [Core Features & User Flows](#core-features--user-flows)
5. [Data Models](#data-models)
6. [UI/UX Requirements](#uiux-requirements)
7. [Deployment & Configuration](#deployment--configuration)

## App Overview & Architecture

### Purpose
DALTI is a comprehensive  appointment booking platform that connects customers with  providers. The system consists of two mobile applications sharing the same backend infrastructure.

### Target Users
- **Customers**: customers seeking  appointments
- **Providers**:  professionals and clinics offering services

### Architecture Overview
- **Frontend**: Flutter mobile applications (Customer & Provider)
- **Backend**: REST API server hosted at `dapi-test.adscloud.org:8443`
- **Database**: Backend database (implementation details not specified)
- **External Services**: Firebase (FCM, Analytics), Payment Gateway
- **Authentication**: JWT-based session management

### App Structure
```
Customer App Package: org.adscloud.dalti.customer
Provider App Package: org.adscloud.dalti.provider
Firebase Project: dalti-prod
```

## API Specifications

### Base Configuration
- **Test Environment**: `https://dapi-test.adscloud.org:8443`
- **Production Environment**: TBD
- **Authentication**: Bearer Token (Session ID)
- **Content Type**: `application/json`

### Authentication Endpoints

#### Login
```http
POST /api/auth/login
Content-Type: application/json

Request Body:
{
  "identifier": "<EMAIL> | +**********",
  "password": "userPassword"
}

Response (200):
{
  "sessionId": "jwt_token_string",
  "message": "Login successful"
}
```

#### User Profile
```http
GET /auth/me
Authorization: Bearer {sessionId}

Response (200):
{
  "id": "user_id",
  "email": "<EMAIL>",
  "firstName": "John",
  "lastName": "Doe",
  "phoneNumber": "+**********",
  "preferredLanguage": "EN"
}
```

#### Registration Flow
```http
# Check if user exists
GET /api/auth/user-exists?email={email}&phoneNumber={phone}

# Request OTP for email registration
POST /api/auth/request-email-otp
{
  "email": "<EMAIL>",
  "firstName": "John",
  "lastName": "Doe",
  "password": "userPassword"
}

# Request OTP for phone registration
POST /api/auth/request-otp
{
  "phoneNumber": "+**********"
}

# Verify OTP and complete registration
POST /api/auth/verify-otp-register
{
  "email": "<EMAIL>",
  "phoneNumber": "+**********",
  "otp": "123456",
  "firstName": "John",
  "lastName": "Doe",
  "password": "userPassword"
}
```

### Password Reset Flow
```http
# Request password reset OTP
POST /api/auth/request-password-reset-otp
{
  "email": "<EMAIL>"
}

# Verify reset OTP
POST /api/auth/verify-password-reset-otp
{
  "email": "<EMAIL>",
  "otp": "123456"
}

# Reset password
POST /api/auth/reset-password
{
  "token": "reset_token",
  "newPassword": "newPassword"
}
```

### Provider & Service Endpoints

#### Provider Search
```http
GET /api/search/providers?q={query}&categoryId={id}&city={city}&skip={offset}&take={limit}

Response:
{
  "providers": [
    {
      "id": "provider_id",
      "name": "Provider Name",
      "specialization": "Cardiology",
      "rating": 4.5,
      "location": "City, Country",
      "services": [...],
      "availability": {...}
    }
  ],
  "total": 100,
  "hasMore": true
}
```

#### Categories
```http
GET /api/provider-categories?targetLanguage={lang}

Response:
[
  {
    "id": 1,
    "name": "Cardiology",
    "description": "Heart specialists",
    "icon": "cardiology_icon.png"
  }
]
```

#### Provider Availability
```http
GET /api/provider-availability?sProvidingPlaceId={id}&serviceId={id}&startDate={date}&endDate={date}&queueId={id}

Response:
{
  "availableSlots": [
    {
      "date": "2024-01-15",
      "timeSlots": ["09:00", "10:00", "11:00"]
    }
  ]
}
```

### Appointment Management

#### Customer Appointments
```http
GET /api/auth/customer/appointments
Authorization: Bearer {sessionId}

Response:
{
  "appointments": [
    {
      "id": "appointment_id",
      "providerId": "provider_id",
      "serviceId": "service_id",
      "dateTime": "2024-01-15T09:00:00Z",
      "status": "confirmed|pending|cancelled",
      "provider": {...},
      "service": {...}
    }
  ]
}
```

#### Book Appointment
```http
POST /api/auth/appointments/customer-booking
Authorization: Bearer {sessionId}

Request:
{
  "providerId": "provider_id",
  "serviceId": "service_id",
  "dateTime": "2024-01-15T09:00:00Z",
  "notes": "Optional notes"
}

Response (201):
{
  "appointmentId": "new_appointment_id",
  "message": "Appointment booked successfully"
}
```

#### Cancel Appointment
```http
PUT /api/auth/appointments/{id}/cancel-customer-request
Authorization: Bearer {sessionId}

DELETE /api/auth/customer/appointments/{id}
Authorization: Bearer {sessionId}
```

### Address Management
```http
# Get addresses
GET /api/auth/customer/addresses
Authorization: Bearer {sessionId}

# Create address
POST /api/auth/customer/addresses
{
  "address": "123 Main St",
  "city": "New York",
  "state": "NY",
  "zipCode": "10001",
  "latitude": 40.7128,
  "longitude": -74.0060,
  "isDefault": true
}

# Update address
PUT /api/auth/customer/addresses/{id}

# Delete address
DELETE /api/auth/customer/addresses/{id}
```

### Notification System
```http
# Get notifications
GET /auth/notifications/mobile/list?limit={limit}&offset={offset}&unreadOnly={boolean}
Authorization: Bearer {sessionId}

# Mark as read
POST /auth/notifications/mobile/mark-as-read
{
  "notificationId": "notification_id"
}

# Mark all as read
POST /auth/notifications/mobile/mark-all-as-read
Authorization: Bearer {sessionId}
```

### Public Endpoints
```http
# Get advertisements
GET /api/auth/public/advertisements

Response:
[
  {
    "id": "ad_id",
    "title": "Advertisement Title",
    "imageUrl": "https://example.com/image.jpg",
    "targetUrl": "https://example.com/landing"
  }
]
```

## External Service Integrations

### Firebase Configuration
```json
{
  "project_info": {
    "project_number": "1060372851323",
    "project_id": "dalti-prod",
    "storage_bucket": "dalti-prod.firebasestorage.app"
  },
  "client": [
    {
      "client_info": {
        "mobilesdk_app_id": "1:1060372851323:android:97fbdd30154b22130690de",
        "android_client_info": {
          "package_name": "org.adscloud.dalti.customer"
        }
      },
      "api_key": [
        {
          "current_key": "AIzaSyDDcsbxchQIzUvlxyr_vddecMVPhxbd6Lg"
        }
      ]
    },
    {
      "client_info": {
        "mobilesdk_app_id": "1:1060372851323:android:c968a0882c726c190690de",
        "android_client_info": {
          "package_name": "org.adscloud.dalti.provider"
        }
      }
    }
  ]
}
```

### Firebase Services
- **Cloud Messaging (FCM)**: Push notifications
- **Analytics**: User behavior tracking
- **Storage**: File uploads (profile pictures, documents)

### Required Firebase Setup
1. Enable Authentication
2. Configure Cloud Messaging
3. Set up Analytics
4. Configure Storage rules
5. Generate and download `google-services.json`

## Core Features & User Flows

### User Registration Flow
1. **Method Selection**: Email or Phone number
2. **User Details**: First name, last name, password
3. **Verification**: OTP via email/SMS
4. **Completion**: Account creation and auto-login

### Login Flow
1. **Identifier Input**: Email or phone number
2. **Password**: User password
3. **Authentication**: Session token generation
4. **Profile Fetch**: User profile data retrieval

### Provider Search Flow
1. **Search Interface**: Text search with filters
2. **Category Filter**:  specializations
3. **Location Filter**: City-based filtering
4. **Results Display**: Paginated provider list
5. **Provider Details**: Detailed provider information

### Appointment Booking Flow
1. **Provider Selection**: Choose  provider
2. **Service Selection**: Select specific service
3. **Date/Time Selection**: Available slot selection
4. **Confirmation**: Booking confirmation
5. **Payment**: Payment processing (if required)

### Profile Management
1. **View Profile**: Display user information
2. **Edit Profile**: Update personal details
3. **Address Management**: CRUD operations for addresses
4. **Language Preferences**: Multi-language support

### Notification System
1. **Push Notifications**: Real-time alerts
2. **In-App Notifications**: Notification center
3. **Read Status**: Mark as read functionality
4. **Notification Types**: Appointment reminders, updates

## Data Models

### User Model
```dart
class User {
  String id;
  String email;
  String phoneNumber;
  String firstName;
  String lastName;
  String preferredLanguage;
  DateTime createdAt;
  DateTime updatedAt;
}
```

### Provider Model
```dart
class Provider {
  String id;
  String name;
  String specialization;
  double rating;
  String location;
  List<Service> services;
  List<String> images;
  String description;
  ContactInfo contactInfo;
}
```

### Appointment Model
```dart
class Appointment {
  String id;
  String customerId;
  String providerId;
  String serviceId;
  DateTime dateTime;
  AppointmentStatus status;
  String notes;
  Provider provider;
  Service service;
}
```

### Address Model
```dart
class Address {
  String id;
  String address;
  String city;
  String state;
  String zipCode;
  double latitude;
  double longitude;
  bool isDefault;
}
```

## UI/UX Requirements

### Design System
- **Primary Colors**: Blue gradient themes
- **Typography**: Custom font weights (400, 600, 800)
- **Components**: Reusable UI components with consistent styling
- **Responsive Design**: Adaptive layouts for different screen sizes

### Key UI Components
1. **Custom Buttons**: Gradient backgrounds, rounded corners
2. **Input Fields**: Consistent styling with validation
3. **Cards**: Provider cards, appointment cards
4. **Navigation**: Bottom tab navigation, drawer navigation
5. **Modals**: Booking confirmation, cancellation dialogs

### Screen Structure
```
├── Splash Screen
├── Intro/Onboarding Screens
├── Authentication
│   ├── Login Screen
│   ├── Signup Screen
│   ├── Forgot Password
│   ├── Verify OTP
│   └── Reset Password
├── Main App
│   ├── Home Tab
│   │   ├── Dashboard
│   │   ├── Upcoming Appointments
│   │   └── Favorite Providers
│   ├── Search Tab
│   │   ├── Provider Search
│   │   ├── Category Filter
│   │   └── Location Filter
│   ├── Bookings Tab
│   │   ├── All Bookings
│   │   ├── Booking Details
│   │   └── Cancel Booking
│   ├── Messages Tab
│   │   └── Chat Interface
│   └── Profile Tab
│       ├── Profile View
│       ├── Edit Profile
│       ├── Address Management
│       ├── Payment Methods
│       ├── Settings
│       └── Notifications
├── Provider Details
├── Booking Flow
│   ├── Service Selection
│   ├── Date/Time Selection
│   ├── Address Selection
│   ├── Payment Screen
│   └── Confirmation
└── Additional Screens
    ├── Help & Support
    ├── Privacy Policy
    ├── Terms of Service
    └── Security Settings
```

### Localization Support
- **Languages**: Multiple language support (EN, AR, etc.)
- **RTL Support**: Right-to-left language support
- **Dynamic Text**: All text should be externalized for translation

## Technical Implementation Details

### Flutter Dependencies
```yaml
dependencies:
  flutter:
    sdk: flutter
  http: ^1.1.0
  shared_preferences: ^2.2.2
  firebase_core: ^2.24.2
  firebase_messaging: ^14.7.10
  firebase_analytics: ^10.8.0
  intl: ^0.19.0
  table_calendar: ^3.0.9
  url_launcher: ^6.2.2
  image_picker: ^1.0.4
  geolocator: ^10.1.0
  permission_handler: ^11.1.0
  flutter_localizations:
    sdk: flutter
```

### State Management
- **Approach**: StatefulWidget with setState (current implementation)
- **Recommendation**: Consider Provider, Riverpod, or Bloc for complex state

### Data Persistence
- **Local Storage**: SharedPreferences for user session and preferences
- **Cache Management**: HTTP response caching for offline support
- **Secure Storage**: For sensitive data like tokens

### Error Handling
- **Network Errors**: Retry mechanisms and offline handling
- **Validation**: Form validation with user-friendly messages
- **Global Error Handler**: Centralized error handling and logging

## Security Considerations

### Authentication Security
- **Token Management**: Secure storage of session tokens
- **Token Refresh**: Automatic token refresh mechanism
- **Logout**: Secure session cleanup

### Data Protection
- **HTTPS**: All API communications over HTTPS
- **Input Validation**: Client and server-side validation
- **Sensitive Data**: Encryption for sensitive user data

### Privacy Compliance
- **Data Collection**: Transparent data collection practices
- **User Consent**: Proper consent mechanisms
- **Data Retention**: Clear data retention policies

## Testing Strategy

### Unit Testing
- **API Services**: Test all HTTP requests and responses
- **Business Logic**: Test core application logic
- **Utilities**: Test helper functions and utilities

### Integration Testing
- **User Flows**: Test complete user journeys
- **API Integration**: Test API integration scenarios
- **External Services**: Test Firebase and other integrations

### UI Testing
- **Widget Testing**: Test individual UI components
- **Screen Testing**: Test complete screen functionality
- **Navigation Testing**: Test app navigation flows

## Performance Optimization

### App Performance
- **Image Optimization**: Compressed images and lazy loading
- **List Performance**: Efficient list rendering with pagination
- **Memory Management**: Proper disposal of resources

### Network Optimization
- **Request Batching**: Combine multiple API requests
- **Caching Strategy**: Implement intelligent caching
- **Offline Support**: Basic offline functionality

## Deployment & Configuration

### Environment Setup

#### Development Environment
```yaml
# config/dev.yaml
api_base_url: "https://dapi-test.adscloud.org:8443"
firebase_project: "dalti-prod"
debug_mode: true
logging_level: "debug"
```

#### Production Environment
```yaml
# config/prod.yaml
api_base_url: "https://api.dalti.com"
firebase_project: "dalti-prod"
debug_mode: false
logging_level: "error"
```

### Required API Keys and Configurations

#### Firebase Configuration
1. **Google Services JSON**: Download from Firebase Console
2. **FCM Server Key**: For push notifications
3. **Analytics Configuration**: Enable Google Analytics

#### Third-Party Services
1. **Payment Gateway**: Configure payment provider
2. **Maps API**: For location services
3. **SMS Provider**: For OTP delivery

### Mobile App Deployment

#### Android Deployment
```gradle
// android/app/build.gradle
android {
    compileSdkVersion 34
    defaultConfig {
        applicationId "org.adscloud.dalti.customer"
        minSdkVersion 21
        targetSdkVersion 34
        versionCode 1
        versionName "1.0.0"
    }

    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile']
            storePassword keystoreProperties['storePassword']
        }
    }
}
```

#### iOS Deployment
```yaml
# ios/Runner/Info.plist
<key>CFBundleIdentifier</key>
<string>org.adscloud.dalti.customer</string>
<key>CFBundleVersion</key>
<string>1.0.0</string>
```

### CI/CD Pipeline
1. **Code Quality**: Linting and formatting checks
2. **Testing**: Automated test execution
3. **Build**: Automated app building
4. **Deployment**: Automated deployment to app stores

### Monitoring and Analytics
1. **Crash Reporting**: Firebase Crashlytics
2. **Performance Monitoring**: Firebase Performance
3. **User Analytics**: Firebase Analytics
4. **API Monitoring**: Backend API monitoring

## Maintenance and Updates

### Version Management
- **Semantic Versioning**: Follow semantic versioning principles
- **Release Notes**: Maintain detailed release notes
- **Backward Compatibility**: Ensure API backward compatibility

### Update Strategy
- **Forced Updates**: Critical security updates
- **Optional Updates**: Feature updates
- **Gradual Rollout**: Phased deployment strategy

### Support and Documentation
- **User Documentation**: In-app help and tutorials
- **Developer Documentation**: Technical documentation
- **API Documentation**: Comprehensive API documentation
- **Troubleshooting Guide**: Common issues and solutions

---

## Conclusion

This technical specification provides a comprehensive blueprint for recreating the DALTI appointment booking application. The document covers all essential aspects from API specifications to deployment considerations, ensuring that a development team has all the necessary information to build a robust, scalable, and user-friendly  appointment booking platform.

For successful implementation, ensure proper project planning, regular testing, and adherence to security best practices throughout the development lifecycle.
