import 'package:http/http.dart' as http;

/// Network configuration class for HTTP client settings
/// Manages timeouts, headers, and common HTTP configurations
class NetworkConfig {
  // Private constructor to prevent instantiation
  NetworkConfig._();

  // ============================================================================
  // TIMEOUT CONFIGURATIONS
  // ============================================================================

  /// Default timeout for API requests (in seconds)
  static const int defaultTimeoutSeconds = 30;

  /// Timeout for file upload requests (in seconds)
  static const int uploadTimeoutSeconds = 120;

  /// Timeout for WebSocket connections (in seconds)
  static const int webSocketTimeoutSeconds = 15;

  // ============================================================================
  // COMMON HEADERS
  // ============================================================================

  /// Get common headers for API requests
  static Map<String, String> get commonHeaders => {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  /// Get headers with authorization token
  static Map<String, String> getAuthHeaders(String token) => {
    ...commonHeaders,
    'Authorization': 'Bearer $token',
  };

  /// Get headers for file upload
  static Map<String, String> getUploadHeaders(String token) => {
    'Authorization': 'Bearer $token',
    // Note: Don't set Content-Type for multipart uploads, let http package handle it
  };

  // ============================================================================
  // HTTP CLIENT CONFIGURATION
  // ============================================================================

  /// Create configured HTTP client with timeout
  static http.Client createHttpClient({int? timeoutSeconds}) {
    final client = http.Client();
    // Note: http package doesn't have built-in timeout,
    // timeout should be handled at request level
    return client;
  }

  /// Get timeout duration
  static Duration getTimeout({int? seconds}) {
    return Duration(seconds: seconds ?? defaultTimeoutSeconds);
  }

  /// Get upload timeout duration
  static Duration get uploadTimeout {
    return Duration(seconds: uploadTimeoutSeconds);
  }

  /// Get WebSocket timeout duration
  static Duration get webSocketTimeout {
    return Duration(seconds: webSocketTimeoutSeconds);
  }

  // ============================================================================
  // REQUEST HELPERS
  // ============================================================================

  /// Make GET request with timeout and common headers
  static Future<http.Response> get(
    Uri url, {
    Map<String, String>? headers,
    int? timeoutSeconds,
  }) async {
    final client = http.Client();
    try {
      final response = await client
          .get(url, headers: headers ?? commonHeaders)
          .timeout(getTimeout(seconds: timeoutSeconds));
      return response;
    } finally {
      client.close();
    }
  }

  /// Make POST request with timeout and common headers
  static Future<http.Response> post(
    Uri url, {
    Map<String, String>? headers,
    Object? body,
    int? timeoutSeconds,
  }) async {
    final client = http.Client();
    try {
      final response = await client
          .post(url, headers: headers ?? commonHeaders, body: body)
          .timeout(getTimeout(seconds: timeoutSeconds));
      return response;
    } finally {
      client.close();
    }
  }

  /// Make PUT request with timeout and common headers
  static Future<http.Response> put(
    Uri url, {
    Map<String, String>? headers,
    Object? body,
    int? timeoutSeconds,
  }) async {
    final client = http.Client();
    try {
      final response = await client
          .put(url, headers: headers ?? commonHeaders, body: body)
          .timeout(getTimeout(seconds: timeoutSeconds));
      return response;
    } finally {
      client.close();
    }
  }

  /// Make DELETE request with timeout and common headers
  static Future<http.Response> delete(
    Uri url, {
    Map<String, String>? headers,
    int? timeoutSeconds,
  }) async {
    final client = http.Client();
    try {
      final response = await client
          .delete(url, headers: headers ?? commonHeaders)
          .timeout(getTimeout(seconds: timeoutSeconds));
      return response;
    } finally {
      client.close();
    }
  }

  // ============================================================================
  // UTILITY METHODS
  // ============================================================================

  /// Check if response is successful (status code 200-299)
  static bool isSuccessful(http.Response response) {
    return response.statusCode >= 200 && response.statusCode < 300;
  }

  /// Check if response indicates unauthorized (401 or 403)
  static bool isUnauthorized(http.Response response) {
    return response.statusCode == 401 || response.statusCode == 403;
  }

  /// Check if response indicates server error (500-599)
  static bool isServerError(http.Response response) {
    return response.statusCode >= 500 && response.statusCode < 600;
  }

  /// Check if response indicates client error (400-499)
  static bool isClientError(http.Response response) {
    return response.statusCode >= 400 && response.statusCode < 500;
  }
}
